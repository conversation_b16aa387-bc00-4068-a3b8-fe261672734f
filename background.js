// 调试日志功能
function logDebug(message, data) {
  if (data) {
    console.log(`[Tristan.Traduction] ${message}`, data);
  } else {
    console.log(`[Tristan.Traduction] ${message}`);
  }
}

// 创建错误通知
function createErrorNotification(tabId, message) {
  try {
    chrome.tabs.sendMessage(tabId, {
      action: "showErrorNotification",
      message: message,
      closeLoading: true
    });
  } catch (error) {
    console.error('发送错误通知失败', error);
  }
}

// 从 apis.json 加载配置
async function loadConfig() {
  try {
    const response = await fetch(chrome.runtime.getURL('apis.json'));
    const config = await response.json();
    
    // 获取当前存储的设置
    chrome.storage.sync.get(['apiType', 'targetLang', 'model'], function(result) {
      // 只有在未设置的情况下才初始化
      const updates = {};
      
      if (!result.apiType) {
        updates.apiType = config.defaultApi;
      }
      
      if (!result.targetLang) {
        updates.targetLang = config.defaultLanguage;
      }
      
      // 如果没有设置模型或API类型已变更
      if (!result.model || (updates.apiType && updates.apiType !== result.apiType)) {
        // 找到当前API
        const currentApi = config.apis.find(api => api.name === (result.apiType || updates.apiType));
        if (currentApi) {
          updates.model = currentApi.defaultModel;
        }
      }
      
      // 只有在有更新时才保存
      if (Object.keys(updates).length > 0) {
        chrome.storage.sync.set(updates);
      }
    });
  } catch (error) {
    console.error('加载配置失败:', error);
  }
}

// 后台脚本初始化
(function() {
  // 监听来自内容脚本的就绪消息
  chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
    if (request.action === "contentScriptReady") {
      sendResponse({ status: 'success' });
    }
    return true;
  });
})();

// 监听插件安装和浏览器启动事件
chrome.runtime.onInstalled.addListener(function() {
  loadConfig();
  createContextMenus();
});

chrome.runtime.onStartup.addListener(function() {
  loadConfig();
  createContextMenus();
});

// 创建右键菜单的函数
function createContextMenus() {
  try {
    // 首先移除所有现有菜单项，避免重复
    chrome.contextMenus.removeAll(function() {
      // 创建统一的右键菜单，同时支持输入框和普通选中文本
      chrome.contextMenus.create({
        id: "tristanTranslate",
        title: "使用 Tristan 翻译",
        contexts: ["editable", "selection"],
        documentUrlPatterns: ["<all_urls>"]
      });
    });
  } catch (e) {
    logDebug('创建右键菜单出错', e);
  }
}

// 统一的 API 调用方法
async function callTranslationAPI(text, apiType, model, targetLanguage, isInEditable = true) {
  try {
    // 从 apis.json 获取 API 配置
    const configResponse = await fetch(chrome.runtime.getURL('apis.json'));
    const config = await configResponse.json();
    const apiConfig = config.apis.find(api => api.name === apiType);
    
    if (!apiConfig) {
      throw new Error(`未找到 ${apiType} 的配置信息`);
    }

    // 记录API调用信息
    logDebug('API 调用请求', {
      selectedText: text,
      apiName: apiType,
      apiEndpoint: apiConfig.endpoint,
      model: model,
      targetLanguage: targetLanguage,
      isInEditable: isInEditable
    });

    const prompt = isInEditable ? 
      `请直接将："${text}"，翻译成${targetLanguage}，无需任何解释或说明以及注释，直接返回翻译结果` :
      `请直接将${targetLanguage}翻译成中文，无需任何解释或说明以及注释，直接返回翻译结果，翻译：${text}`;

    const requestBody = {
      model: model,
      messages: [
        {
          role: "user",
          content: prompt
        }
      ],
      // 固定请求参数
      temperature: 0.7,

      
      //模型的预设参数


      //Qwen3系列，是否开启思考，默认关闭(true:开启，false:关闭)
      //"enable_thinking": false,

      //Doubao1.6系列，是否开启思考，默认开启(enabled:开启，disabled:关闭,auto:自动)
      "thinking":{"type":"disabled"}


    };

    // 记录请求体
    logDebug('API 原始请求', requestBody);

    const apiResponse = await fetch(apiConfig.endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiConfig.key}`
      },
      body: JSON.stringify(requestBody)
    });

    if (!apiResponse.ok) {
      throw new Error(`API 响应错误: ${apiResponse.status} ${apiResponse.statusText}`);
    }

    const data = await apiResponse.json();
    logDebug('API 原始响应', data);

    // 从响应中提取翻译结果
    const translatedText = data.choices[0].message.content.trim();
    logDebug('API 翻译结果', translatedText);

    if (!translatedText) {
      throw new Error('无法从响应中提取翻译结果');
    }

    return translatedText;
  } catch (error) {
    logDebug('API 调用出错', error);
    throw error;
  }
}

// 修改翻译处理函数
async function handleTranslation(text, apiType, model, targetLang, isInEditable = true) {
  try {
    return await callTranslationAPI(text, apiType, model, targetLang, isInEditable);
  } catch (error) {
    logDebug('翻译处理出错', error);
    throw error;
  }
}

// 修改消息监听器
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'translate') {
    chrome.storage.sync.get(['apiType', 'targetLang', 'model'], async function(result) {
      const apiType = result.apiType;
      const targetLang = result.targetLang;

      try {
        // 从 apis.json 获取当前 API 的模型信息
        const configResponse = await fetch(chrome.runtime.getURL('apis.json'));
        const config = await configResponse.json();
        const currentApi = config.apis.find(api => api.name === apiType);
        
        if (!currentApi) {
          throw new Error(`未找到 ${apiType} 的配置信息`);
        }

        // 使用用户选择的模型，如果没有则使用默认模型
        const modelToUse = result.model && currentApi.models.includes(result.model) 
          ? result.model 
          : currentApi.defaultModel;

        const translatedText = await handleTranslation(
          request.text, 
          apiType, 
          modelToUse, 
          targetLang, 
          request.isInEditable
        );
        sendResponse({ success: true, translatedText });
      } catch (error) {
        sendResponse({ success: false, error: error.message });
      }
    });
    return true;
  }
});

// 监听右键菜单点击事件
chrome.contextMenus.onClicked.addListener(function(info, tab) {
  if (info.menuItemId === "tristanTranslate" && info.selectionText) {
    handleTranslationRequest(info, tab);
  }
});

// 处理翻译请求
async function handleTranslationRequest(info, tab) {
  try {
    // 获取当前设置
    const result = await chrome.storage.sync.get(['apiType', 'targetLang', 'model']);
    const apiType = result.apiType;
    const targetLang = result.targetLang;
    
    // 获取当前API的模型信息
    const response = await fetch(chrome.runtime.getURL('apis.json'));
    const config = await response.json();
    const currentApi = config.apis.find(api => api.name === apiType);
    
    if (!currentApi) {
      throw new Error(`未找到 ${apiType} 的配置信息`);
    }
    
    // 使用用户选择的模型，如果没有则使用默认模型
    const modelToUse = result.model && currentApi.models.includes(result.model) 
      ? result.model 
      : currentApi.defaultModel;
    
    // 发送加载状态
    await chrome.tabs.sendMessage(tab.id, { action: "showLoading" });
    
    // 判断是否在可编辑区域内选择的文本
    const isInEditable = info.editable === true;
    
    // 执行翻译
    const translatedText = await handleTranslation(
      info.selectionText,
      apiType,
      modelToUse,
      targetLang,
      isInEditable
    );
    
    // 发送翻译结果
    await chrome.tabs.sendMessage(tab.id, { 
      action: "showTranslation", 
      text: translatedText 
    });
    
  } catch (error) {
    console.error('翻译请求处理失败:', error);
    // 发送错误通知
    await chrome.tabs.sendMessage(tab.id, { 
      action: "showErrorNotification", 
      message: error.message || '翻译失败，请稍后重试' 
    });
  }
}

// 监听快捷键命令
chrome.commands.onCommand.addListener(async (command) => {
  if (command === "translate-selection") {
    try {
      // 获取当前活动标签页
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      const activeTab = tabs[0];

      if (!activeTab) {
        throw new Error('无法获取当前活动标签页');
      }

      // 执行脚本获取选中的文本
      const results = await chrome.scripting.executeScript({
        target: { tabId: activeTab.id },
        func: () => {
          const selection = window.getSelection();
          return selection ? selection.toString().trim() : '';
        }
      });

      const selectedText = results[0].result;

      if (!selectedText) {
        // 如果没有选中文本，显示错误通知
        // await chrome.tabs.sendMessage(activeTab.id, {
        //   action: "showErrorNotification",
        //   message: "请先选择要翻译的文本"
        // });
        return;
      }

      // 判断选中文本是否在可编辑区域
      const isInEditableResults = await chrome.scripting.executeScript({
        target: { tabId: activeTab.id },
        func: () => {
          const activeElement = document.activeElement;
          const isEditable = activeElement && 
            (activeElement.isContentEditable || 
             ['INPUT', 'TEXTAREA'].includes(activeElement.tagName) ||
             activeElement.getAttribute('role') === 'textbox');
          return isEditable;
        }
      });

      const isInEditable = isInEditableResults[0].result;

      // 处理与右键菜单点击相同的翻译流程
      await handleTranslationRequest({
        selectionText: selectedText,
        editable: isInEditable
      }, activeTab);

    } catch (error) {
      console.error('快捷键翻译处理失败:', error);
      // 尝试向当前标签页发送错误通知
      try {
        const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
        if (tabs[0]) {
          await chrome.tabs.sendMessage(tabs[0].id, {
            action: "showErrorNotification",
            message: error.message || '快捷键翻译失败，请稍后重试'
          });
        }
      } catch (notifyError) {
        console.error('无法发送错误通知:', notifyError);
      }
    }
  }
}); 