/* 插件设置页面样式 */
.translate-extension-popup {
  width: 320px;
  padding: 24px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Aria<PERSON>, sans-serif;
  background: #ffffff;
  color: #333;
  margin: 0;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.translate-extension-popup * {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

.translate-extension-popup .container {
  padding: 20px;
}

.translate-extension-popup .header {
  text-align: center;
  margin-bottom: 24px;
}

.translate-extension-popup h1 {
  margin: 0;
  font-size: 24px;
  color: #333;
  font-weight: 600;
}


.translate-extension-popup .settings {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 24px;
}

.translate-extension-popup .setting-item {
  margin-bottom: 16px;
}

.translate-extension-popup .setting-item:last-child {
  margin-bottom: 0;
}

.translate-extension-popup label {
  display: block;
  margin-bottom: 8px;
  color: #333;
  font-size: 14px;
  font-weight: 500;
}

.translate-extension-popup select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background-color: white;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.translate-extension-popup select:hover {
  border-color: #4a90e2;
}

.translate-extension-popup select:focus {
  outline: none;
  border-color: #4a90e2;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.1);
}

/* API密钥输入框样式 */
.translate-extension-popup input[type="text"] {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background-color: white;
  font-size: 14px;
  color: #333;
  transition: all 0.2s ease;
}

.translate-extension-popup input[type="text"]:hover {
  border-color: #4a90e2;
}

.translate-extension-popup input[type="text"]:focus {
  outline: none;
  border-color: #4a90e2;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

.translate-extension-popup input[type="text"]::placeholder {
  color: #aaa;
  font-style: italic;
}

.translate-extension-popup .features {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
}

.translate-extension-popup .features h2 {
  margin: 0 0 12px;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.translate-extension-popup .features p {
  margin: 8px 0;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

/* 插件弹出窗口样式 */
#translate-popup {
  position: fixed;
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  padding: 12px;
  z-index: 2147483647;
  max-width: 300px;
  display: none;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

#translate-popup button {
  background: #3498db;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  margin-top: 8px;
  width: 100%;
  transition: background-color 0.3s ease;
}

#translate-popup button:hover {
  background: #2980b9;
}

#translate-popup button:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
}

#translate-popup .translate-result {
  margin-top: 8px;
  padding: 8px;
  border-top: 1px solid #eee;
  font-size: 14px;
  color: #34495e;
  word-break: break-word;
  line-height: 1.5;
}

/* 确保插件样式不会影响网页其他元素 */
#translate-popup * {
  box-sizing: border-box;
}

/* 加载动画样式 */
.tristan-translate-loading {
  display: flex;
  align-items: center;
  background: #ffffff;
  padding: 8px 12px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 10000;
}

.tristan-translate-loading-spinner {
  width: 20px;
  height: 20px;
  margin-right: 8px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #4a90e2;
  border-radius: 50%;
  animation: tristan-spin 1s linear infinite;
}

.tristan-translate-loading-text {
  color: #666;
  font-size: 14px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

@keyframes tristan-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 成功通知样式 */
.tristan-translate-notification {
  padding: 12px 16px;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 999999;
  display: flex;
  align-items: center;
  max-width: 300px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  animation: tristan-translate-fade-in 0.3s ease-out;
}

/* 错误通知样式 */
.tristan-translate-error {
  background-color: #ffffff;
  border: 1px solid #f3f3f3;
  color: #666;
  font-size: 14px;
  padding: 8px 12px;
  max-width: 280px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tristan-translate-notification-icon {
  font-size: 20px;
  margin-right: 10px;
  font-weight: bold;
}

.tristan-translate-notification-message {
  flex: 1;
  font-size: 14px;
  line-height: 1.4;
}

.tristan-translate-notification-close {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 0 0 0 10px;
  color: inherit;
  opacity: 0.7;
}

.tristan-translate-notification-close:hover {
  opacity: 1;
}

@keyframes tristan-translate-fade-in {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
} 