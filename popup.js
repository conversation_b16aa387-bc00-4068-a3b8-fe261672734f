document.addEventListener('DOMContentLoaded', async function() {
  // 获取设置元素
  const apiTypeSelect = document.getElementById('apiType');
  const modelSelect = document.getElementById('modelSelect');
  const targetLangSelect = document.getElementById('targetLang');
  
  // 从 apis.json 加载配置
  let config;
  try {
    const response = await fetch(chrome.runtime.getURL('apis.json'));
    config = await response.json();
  } catch (error) {
    console.error('加载配置失败:', error);
    return;
  }

  // 填充 API 选项
  config.apis.forEach(api => {
    const option = document.createElement('option');
    option.value = api.name;
    option.textContent = api.name;
    apiTypeSelect.appendChild(option);
  });

  // 填充语言选项
  config.languages.forEach(lang => {
    const option = document.createElement('option');
    option.value = lang;
    option.textContent = lang;
    targetLangSelect.appendChild(option);
  });

  // 填充模型选项
  function populateModelOptions(models) {
    modelSelect.innerHTML = ''; // 清空现有选项
    models.forEach(model => {
      const option = document.createElement('option');
      option.value = model;
      option.textContent = model;
      modelSelect.appendChild(option);
    });
  }

  // 加载保存的设置
  chrome.storage.sync.get(['apiType', 'targetLang', 'model'], function(result) {
    // 设置API类型
    if (result.apiType) {
      apiTypeSelect.value = result.apiType;
    } else {
      apiTypeSelect.value = config.defaultApi;
      chrome.storage.sync.set({ apiType: config.defaultApi });
    }
    
    // 设置目标语言
    if (result.targetLang) {
      targetLangSelect.value = result.targetLang;
    } else {
      targetLangSelect.value = config.defaultLanguage;
      chrome.storage.sync.set({ targetLang: config.defaultLanguage });
    }
    
    // 设置模型选择
    updateModelOptions(result.apiType || config.defaultApi, result.model);
  });

  // API类型切换时更新模型选择器
  apiTypeSelect.addEventListener('change', function() {
    const apiType = apiTypeSelect.value;
    // 获取当前保存的模型，看是否可以在新API中使用
    chrome.storage.sync.get(['model'], function(result) {
      updateModelOptions(apiType, result.model);
    });
    chrome.storage.sync.set({ apiType: apiType });
  });

  // 更新模型选项
  function updateModelOptions(apiType, savedModel) {
    const currentApi = config.apis.find(api => api.name === apiType);
    if (currentApi) {
      populateModelOptions(currentApi.models);
      
      // 如果有保存的模型且在当前API的模型列表中，则选择该模型
      if (savedModel && currentApi.models.includes(savedModel)) {
        modelSelect.value = savedModel;
      } else {
        // 否则使用API的默认模型
        modelSelect.value = currentApi.defaultModel;
        // 保存这个新的默认模型到存储中
        chrome.storage.sync.set({ model: currentApi.defaultModel });
      }
    }
  }

  // 模型选择变更时保存设置
  modelSelect.addEventListener('change', function() {
    chrome.storage.sync.set({ model: modelSelect.value });
  });

  // 目标语言选择变更时保存设置
  targetLangSelect.addEventListener('change', function() {
    chrome.storage.sync.set({ targetLang: targetLangSelect.value });
  });
}); 