<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Tristan：人工智能翻译助手</title>
  <style>
    body {
      width: 320px;
      padding: 24px;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Aria<PERSON>, sans-serif;
      background: #ffffff;
      color: #333;
      margin: 0;
      border-radius: 8px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    }
    .header {
      text-align: center;
      margin-bottom: 20px;
    }
    .header h1 {
      color: #333;
      font-size: 28px;
      margin: 0;
      padding: 10px 0;
    }
    .setting-group {
      background-color: white;
      padding: 15px;
      margin-bottom: 15px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    label {
      display: block;
      margin-bottom: 8px;
      font-weight: bold;
      color: #444;
    }
    select {
      width: 100%;
      padding: 8px;
      margin-bottom: 5px;
      border: 1px solid #ddd;
      border-radius: 4px;
      background-color: white;
      font-size: 14px;
    }
    select:focus {
      outline: none;
      border-color: #4a90e2;
      box-shadow: 0 0 0 2px rgba(74,144,226,0.2);
    }
    .features {
      background-color: white;
      padding: 15px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .features h2 {
      color: #333;
      font-size: 16px;
      margin: 0 0 10px 0;
    }
    .features p {
      color: #666;
      margin: 5px 0;
      font-size: 14px;
      line-height: 1.4;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>Tristan.Traduction</h1>
  </div>

  <div class="setting-group">
    <label for="apiType">选择 API：</label>
    <select id="apiType"></select>
  </div>

  <div class="setting-group">
    <label for="modelSelect">选择模型：</label>
    <select id="modelSelect"></select>
  </div>

  <div class="setting-group">
    <label for="targetLang">目标语言：</label>
    <select id="targetLang"></select>
  </div>

  <div class="features">
    <h2>使用说明</h2>
    <p>1. 在任意输入框中选中需要翻译的文字</p>
    <p>2. 右键点击，选择"使用 Tristan 翻译"</p>
    <p>3. 翻译结果将自动替换输入框中的内容</p>
  </div>

  <script src="popup.js"></script>
</body>
</html> 