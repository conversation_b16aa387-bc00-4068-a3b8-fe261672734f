// 调试日志功能
function logDebug(message, data) {
  console.log(`[Tristan.Traduction]`, `${message}`, `${data || ''}`);
}

// 初始化脚本
(function() {
  logDebug('内容脚本已加载');
  
  // 向后台脚本发送就绪消息
  chrome.runtime.sendMessage({
    action: "contentScriptReady"
  }, function(response) {
    logDebug('后台脚本已响应就绪消息', response);
  });
})();

// 设置元素样式的辅助函数
function setStyles(element, styles) {
  Object.keys(styles).forEach(property => {
    element.style[property] = styles[property];
  });
}

// 计算弹窗位置的公共函数
function calculatePosition(activeElement, padding = 0) {
  let position = {};
  
  // 如果找到活动输入元素，则将弹窗放在其右侧
  if (activeElement && activeElement.getBoundingClientRect) {
    const rect = activeElement.getBoundingClientRect();
    
    // 获取视口尺寸
    const viewportWidth = window.innerWidth || document.documentElement.clientWidth;
    const viewportHeight = window.innerHeight || document.documentElement.clientHeight;
    
    // 设置容器的最大尺寸
    const containerWidth = 320; // 容器的最大宽度
    const containerHeight = 220; // 容器的最大高度
    
    // 计算初始位置：默认在输入框的右侧，顶部对齐
    let top = rect.top;
    let left = rect.right + 10;
    
    // 检查右侧空间是否足够
    if (left + containerWidth > viewportWidth - padding) {
      // 如果右侧空间不足，向左移动容器，使其完全显示在视口内
      left = Math.max(padding, viewportWidth - containerWidth - padding);
    }
    
    // 检查底部空间是否足够，只有当内容实际超出容器最大高度时才调整
    const contentHeight = activeElement.offsetHeight || 0;
    if (contentHeight > containerHeight && top + containerHeight > viewportHeight - padding) {
      // 如果底部空间不足，尝试向上移动
      top = Math.max(padding, viewportHeight - containerHeight - padding);
    }
    
    position = {
      position: 'fixed',
      top: `${top}px`,
      left: `${left}px`
    };
    
    logDebug('计算后的容器位置', position);
  } else {
    // 如果没有找到活动元素，默认放在页面中央
    position = {
      position: 'fixed',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)'
    };
    
    logDebug('未找到活动元素，将弹窗放在页面中央');
  }
  
  return position;
}

// 计算提示框位置的新函数
function calculatePopupPosition(anchorRect, isTranslationResult = false) {
  const viewportWidth = window.innerWidth || document.documentElement.clientWidth;
  const viewportHeight = window.innerHeight || document.documentElement.clientHeight;
  
  // 容器最大尺寸
  const maxWidth = 320;
  const maxHeight = 220;
  
  // 默认位置：顶部对齐选中文字顶部，左边位于选中文字右边
  let top = anchorRect.top;
  let left = anchorRect.right + 10;
  
  // 计算容器实际尺寸（需要先添加到DOM才能获取，这里使用最大尺寸作为参考）
  const containerWidth = Math.min(anchorRect.width + 40, maxWidth);
  const containerHeight = Math.min(anchorRect.height * 2, maxHeight);
  
  // 对于翻译结果提示框
  if (isTranslationResult) {
    // 情况1: 底部超出视口
    const bottomOverflow = (top + containerHeight) > viewportHeight;
    // 情况2: 右侧超出视口
    const rightOverflow = (left + containerWidth) > viewportWidth;
    
    if (bottomOverflow && rightOverflow) {
      // 情况5: 底部和右侧都超出 - 放在右下角
      top = viewportHeight - containerHeight - 10;
      left = viewportWidth - containerWidth - 10;
    } else if (bottomOverflow) {
      // 情况3: 仅底部超出 - 底部对齐视口底部
      top = viewportHeight - containerHeight - 10;
    } else if (rightOverflow) {
      // 情况4: 仅右侧超出 - 右侧对齐视口右侧
      left = viewportWidth - containerWidth - 10;
    }
    // 情况2: 默认位置不超出视口 - 不做调整
  } 
  // 对于"翻译中..."提示框
  else {
    // 仅检查右侧是否超出
    if ((left + containerWidth) > viewportWidth) {
      // 右侧超出 - 右侧对齐视口右侧
      left = viewportWidth - containerWidth - 10;
    }
  }
  
  return {
    position: 'fixed',
    top: `${Math.max(10, top)}px`,
    left: `${Math.max(10, left)}px`,
    maxWidth: `${maxWidth}px`,
    maxHeight: `${maxHeight}px`
  };
}

// 创建加载动画
function createLoadingIndicator() {
  // 先移除任何现有的加载指示器
  const existingIndicators = document.querySelectorAll('.tristan-translate-loading');
  existingIndicators.forEach(indicator => {
    try {
      indicator.remove();
    } catch (e) {
      // 忽略可能的错误
    }
  });
  
  // 获取当前活动的输入元素
  const activeElement = getSelectedInputElement() || document.activeElement;
  
  const loadingDiv = document.createElement('div');
  loadingDiv.className = 'tristan-translate-loading';
  loadingDiv.innerHTML = `
    <div class="tristan-translate-loading-spinner"></div>
    <div class="tristan-translate-loading-text">翻译中...</div>
  `;
  
  // 获取定位信息 - 如果没有选中文本则使用默认位置
  let position;
  const selectionRect = getSelectionRect();
  if (selectionRect) {
    position = calculatePopupPosition(selectionRect, false);
  } else {
    position = calculatePosition(activeElement);
  }
  
  // 设置加载指示器样式
  setStyles(loadingDiv, {
    ...position,
    display: 'flex',
    alignItems: 'center',
    background: '#ffffff',
    padding: '8px 12px',
    borderRadius: '6px',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
    zIndex: '10000',
    pointerEvents: 'none',
    userSelect: 'none',
    width: 'auto',
    maxWidth: '320px',
    height: 'auto',
    maxHeight: '220px',
    wordBreak: 'break-word',
    whiteSpace: 'pre-wrap'
  });
  
  // 为spinner设置样式
  const spinner = loadingDiv.querySelector('.tristan-translate-loading-spinner');
  setStyles(spinner, {
    width: '20px',
    height: '20px',
    marginRight: '8px',
    border: '2px solid #f3f3f3',
    borderTop: '2px solid #4a90e2',
    borderRadius: '50%',
    animation: 'tristan-translate-spin 1s linear infinite',
    flexShrink: '0'
  });
  
  // 为文本设置样式
  const text = loadingDiv.querySelector('.tristan-translate-loading-text');
  setStyles(text, {
    color: '#666',
    fontSize: '14px',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    whiteSpace: 'nowrap',
    flexShrink: '1'
  });
  
  // 添加keyframes动画
  if (!document.getElementById('tristan-translate-keyframes')) {
    const style = document.createElement('style');
    style.id = 'tristan-translate-keyframes';
    style.textContent = `
      @keyframes tristan-translate-spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    `;
    if (document.head) {
      document.head.appendChild(style);
    } else {
      // 如果head还不存在，等待document完成加载
      const appendStyleWhenReady = () => {
        if (document.head) {
          document.head.appendChild(style);
        } else {
          setTimeout(appendStyleWhenReady, 10);
        }
      };
      setTimeout(appendStyleWhenReady, 10);
    }
  }
  
  // 添加到文档并确保它可见
  try {
    if (document.body) {
      document.body.appendChild(loadingDiv);
      
      // 确保指示器可见 - 设置一个定时器检查是否被其他代码隐藏或移除
      window.tristanLoadingTimer = setInterval(() => {
        if (document.body && !document.body.contains(loadingDiv)) {
          try {
            document.body.appendChild(loadingDiv);
          } catch (e) {
            // 忽略错误
          }
        }
        
        // 确保它可见
        if (loadingDiv.style.display === 'none') {
          loadingDiv.style.display = 'flex';
        }
        
        // 10秒后清除检查
        setTimeout(() => {
          if (window.tristanLoadingTimer) {
            clearInterval(window.tristanLoadingTimer);
            window.tristanLoadingTimer = null;
          }
        }, 10000);
      }, 500);
    } else {
      // 如果body还不存在，等待document完成加载
      const appendDivWhenReady = () => {
        if (document.body) {
          document.body.appendChild(loadingDiv);
          
          // 设置相同的检查定时器
          window.tristanLoadingTimer = setInterval(() => {
            if (document.body && !document.body.contains(loadingDiv)) {
              try {
                document.body.appendChild(loadingDiv);
              } catch (e) {
                // 忽略错误
              }
            }
            
            // 确保它可见
            if (loadingDiv.style.display === 'none') {
              loadingDiv.style.display = 'flex';
            }
            
            // 10秒后清除检查
            setTimeout(() => {
              if (window.tristanLoadingTimer) {
                clearInterval(window.tristanLoadingTimer);
                window.tristanLoadingTimer = null;
              }
            }, 10000);
          }, 500);
        } else {
          setTimeout(appendDivWhenReady, 10);
        }
      };
      setTimeout(appendDivWhenReady, 10);
    }
  } catch (e) {
    logDebug('添加加载指示器时出错', e);
  }
  
  return loadingDiv;
}

// 创建错误通知
function createErrorNotification(message) {
  try {
    // 删除现有通知
    const existingNotifications = document.querySelectorAll('.tristan-translate-notification');
    existingNotifications.forEach(notification => notification.remove());
    
    // 创建新通知
    const notification = document.createElement('div');
    notification.className = 'tristan-translate-notification tristan-translate-error';
    
    // 添加内容
    notification.innerHTML = `
      <div class="tristan-translate-notification-message">${message}</div>
    `;
    
    // 获取当前活动的输入元素
    const activeElement = getSelectedInputElement() || document.activeElement;
    
    // 获取定位信息
    const position = calculatePosition(activeElement);
    
    // 应用位置样式
    setStyles(notification, {...position, bottom: 'auto', right: 'auto'});
    
    // 添加到页面
    if (document.body) {
      document.body.appendChild(notification);
    } else {
      // 如果body还不存在，等待document完成加载
      const appendNotificationWhenReady = () => {
        if (document.body) {
          document.body.appendChild(notification);
        } else {
          setTimeout(appendNotificationWhenReady, 10);
        }
      };
      setTimeout(appendNotificationWhenReady, 10);
    }
    
    // 3秒后自动移除
    setTimeout(() => {
      if (document.body && document.body.contains(notification)) {
        // 添加淡出动画
        setStyles(notification, {
          transition: 'opacity 0.5s ease-out',
          opacity: '0'
        });
        
        // 移除元素
        setTimeout(() => {
          if (document.body && document.body.contains(notification)) {
            notification.remove();
          }
        }, 500);
      }
    }, 3000);
    
    return notification;
  } catch (error) {
    console.error('创建错误通知失败', error);
    return null;
  }
}

// 获取选中的输入框元素
function getSelectedInputElement() {
  try {
    // 检查是否有处于焦点的输入元素
    if (document.activeElement && 
        (document.activeElement.tagName === 'INPUT' || 
         document.activeElement.tagName === 'TEXTAREA' ||
         document.activeElement.isContentEditable ||
         document.activeElement.getAttribute('role') === 'textbox')) {
      
      logDebug('找到激活的输入元素', document.activeElement);
      return document.activeElement;
    }
    
    // 如果没有活动输入元素，通过选区查找
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) {
      return null;
    }

    const range = selection.getRangeAt(0);
    let element = range.commonAncestorContainer;
    
    // 如果选中的是文本节点，获取其父元素
    if (element.nodeType === Node.TEXT_NODE) {
      element = element.parentElement;
    }
    
    logDebug('从选区获取到元素', element);

    // 向上查找最近的输入框元素
    while (element && element.nodeType === Node.ELEMENT_NODE) {
      // 检查常见输入类型
      if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
        logDebug('找到标准输入元素', element);
        return element;
      }
      
      // 检查contenteditable
      if (element.isContentEditable) {
        logDebug('找到contenteditable元素', element);
        return element;
      }
      
      // 检查ARIA角色
      if (element.getAttribute('role') === 'textbox') {
        logDebug('找到具有textbox角色的元素', element);
        return element;
      }
      
      // 检查常见富文本编辑器的编辑区域
      if (element.classList.contains('ql-editor') ||
          element.classList.contains('editor-content') ||
          element.classList.contains('CodeMirror') ||
          element.classList.contains('ace_editor') ||
          element.classList.contains('monaco-editor') ||
          element.classList.contains('ProseMirror') ||
          element.classList.contains('trix-content')) {
        logDebug('找到富文本编辑器元素', element);
        return element;
      }
      
      element = element.parentElement;
    }
    
    // 如果还是没找到，尝试找特定的iframe编辑器
    const frames = document.querySelectorAll('iframe');
    for (const frame of frames) {
      try {
        const frameDoc = frame.contentDocument || frame.contentWindow.document;
        if (frameDoc && frameDoc.activeElement) {
          if (frameDoc.activeElement.tagName === 'BODY' && frameDoc.body.isContentEditable) {
            logDebug('找到iframe内的可编辑body元素', frameDoc.body);
            return frameDoc.body;
          }
          if (frameDoc.activeElement.tagName === 'INPUT' || 
              frameDoc.activeElement.tagName === 'TEXTAREA' ||
              frameDoc.activeElement.isContentEditable) {
            logDebug('找到iframe内的输入元素', frameDoc.activeElement);
            return frameDoc.activeElement;
          }
        }
      } catch (e) {
        // 跨域iframe会抛出错误，忽略继续
      }
    }
    
    return null;
  } catch (error) {
    logDebug('查找输入元素时出错', error);
    return null;
  }
}

// 设置输入框的值
function setInputValue(inputElement, value) {
  if (!inputElement) {
    logDebug('没有提供输入元素');
    return false;
  }
  
  // 确保值是一个字符串
  if (typeof value !== 'string') {
    try {
      if (value && value.toString) {
        value = value.toString();
      } else {
        value = JSON.stringify(value);
      }
      logDebug('将非字符串值转换为字符串', value);
    } catch (e) {
      logDebug('转换值类型失败', e);
      return false;
    }
  }
  
  // 修剪文本，去除不必要的引号和空白
  value = value.trim();
  if ((value.startsWith('"') && value.endsWith('"')) || 
      (value.startsWith("'") && value.endsWith("'"))) {
    // 去除首尾的引号，可能是API响应格式问题
    value = value.substring(1, value.length - 1);
    logDebug('去除文本中的引号后', value);
  }
  
  try {
    logDebug('设置输入值', { 
      elementType: inputElement.tagName, 
      isContentEditable: inputElement.isContentEditable, 
      className: inputElement.className,
      valueLength: value.length
    });
    
    // 尝试方法1: 直接设置value属性（标准输入元素）
    if (inputElement.tagName === 'INPUT' || inputElement.tagName === 'TEXTAREA') {
      try {
        // 保存当前光标位置
        const startPos = inputElement.selectionStart;
        const endPos = inputElement.selectionEnd;
        
        // 确保有选中内容
        if (startPos !== undefined && endPos !== undefined && startPos !== endPos) {
          // 部分替换
          const beforeSelection = inputElement.value.substring(0, startPos);
          const afterSelection = inputElement.value.substring(endPos);
          const originalValue = inputElement.value;
          
          inputElement.value = beforeSelection + value + afterSelection;
          
          // 如果赋值成功，设置光标位置
          if (inputElement.value !== originalValue) {
            const newCursorPos = startPos + value.length;
            inputElement.selectionStart = newCursorPos;
            inputElement.selectionEnd = newCursorPos;
            
            // 触发各种事件
            simulateInputEvents(inputElement);
            return true;
          }
        } else {
          // 没有明确的选区，替换全部内容
          const originalValue = inputElement.value;
          inputElement.value = value;
          
          if (inputElement.value !== originalValue) {
            // 触发各种事件
            simulateInputEvents(inputElement);
            return true;
          }
        }
      } catch (e) {
        logDebug('直接设置value失败', e);
      }
    }
    
    // 尝试方法2: 使用execCommand（contenteditable元素）
    if (inputElement.isContentEditable || 
        document.activeElement === inputElement || 
        inputElement.getAttribute('role') === 'textbox') {
      try {
        // 确保元素处于焦点
        inputElement.focus();
        
        // 检查是否有当前选区
        const selection = window.getSelection();
        if (selection.rangeCount > 0) {
          // 清除当前选择内容并插入新文本
          document.execCommand('insertText', false, value);
          
          // 触发input和change事件
          simulateInputEvents(inputElement);
          return true;
        } else {
          // 如果没有当前选区，创建一个新的
          const range = document.createRange();
          range.selectNodeContents(inputElement);
          selection.removeAllRanges();
          selection.addRange(range);
          
          // 替换所有内容
          document.execCommand('insertText', false, value);
          
          // 触发input和change事件
          simulateInputEvents(inputElement);
          return true;
        }
      } catch (e) {
        logDebug('使用execCommand失败', e);
      }
    }
    
    // 尝试方法3: 使用innerHTML（对于富文本编辑器）
    if (inputElement.innerHTML !== undefined) {
      try {
        const originalHTML = inputElement.innerHTML;
        inputElement.innerHTML = value;
        
        if (inputElement.innerHTML !== originalHTML) {
          simulateInputEvents(inputElement);
          return true;
        }
      } catch (e) {
        logDebug('设置innerHTML失败', e);
      }
    }
    
    // 尝试方法4: 使用textContent
    if (inputElement.textContent !== undefined) {
      try {
        const originalText = inputElement.textContent;
        inputElement.textContent = value;
        
        if (inputElement.textContent !== originalText) {
          simulateInputEvents(inputElement);
          return true;
        }
      } catch (e) {
        logDebug('设置textContent失败', e);
      }
    }
    
    // 额外尝试: 通过键盘事件模拟键入
    try {
      // 聚焦元素
      inputElement.focus();
      
      // 先选中所有内容
      if (inputElement.select) {
        inputElement.select();
      } else if (window.getSelection) {
        const selection = window.getSelection();
        const range = document.createRange();
        range.selectNodeContents(inputElement);
        selection.removeAllRanges();
        selection.addRange(range);
      }
      
      // 尝试删除现有内容并输入新内容
      document.execCommand('delete', false);
      document.execCommand('insertText', false, value);
      simulateInputEvents(inputElement);
      return true;
    } catch (e) {
      logDebug('模拟键盘输入失败', e);
    }
    
    // 如果所有方法都失败，返回失败
    logDebug('所有替换方法都失败');
    return false;
  } catch (error) {
    logDebug('设置输入值时发生严重错误', error);
    return false;
  }
}

// 模拟输入事件
function simulateInputEvents(element) {
  try {
    // 创建并分发input事件
    const inputEvent = new Event('input', { bubbles: true, cancelable: true });
    element.dispatchEvent(inputEvent);
    
    // 创建并分发change事件
    const changeEvent = new Event('change', { bubbles: true });
    element.dispatchEvent(changeEvent);
    
    // 对于React等现代框架
    if (typeof InputEvent === 'function') {
      try {
        const modernInputEvent = new InputEvent('input', { 
          bubbles: true, 
          cancelable: true,
          inputType: 'insertText'
        });
        element.dispatchEvent(modernInputEvent);
      } catch (e) {
        // 某些浏览器可能不支持InputEvent的所有特性
      }
    }
    
    // 尝试直接调用事件处理程序
    if (typeof element.oninput === 'function') {
      element.oninput();
    }
    
    if (typeof element.onchange === 'function') {
      element.onchange();
    }
  } catch (e) {
    logDebug('模拟输入事件失败', e);
  }
}

// 获取选中的文本信息
function getSelectedText() {
  const selection = window.getSelection();
  if (selection && selection.rangeCount > 0) {
    return selection.toString();
  }
  
  return '';
}

// 获取选中文本位置
function getSelectionRect() {
  const selection = window.getSelection();
  if (!selection || selection.rangeCount === 0) {
    return null;
  }
  
  const range = selection.getRangeAt(0);
  return range.getBoundingClientRect();
}

// 创建普通文本翻译提示框
function createNormalTextTranslationPopup(translatedText, selectionRect) {
  // 删除之前存在的提示框
  const existingPopups = document.querySelectorAll('.tristan-translate-normal-popup');
  existingPopups.forEach(popup => {
    try {
      popup.remove();
    } catch (e) {
      // 忽略可能的错误
    }
  });
  
  // 创建提示框
  const popup = document.createElement('div');
  popup.className = 'tristan-translate-normal-popup tristan-translate-notification';
  
  // 添加内容
  popup.innerHTML = `
    <div class="tristan-translate-normal-popup-content">${translatedText}</div>
    <div class="tristan-translate-normal-popup-close">×</div>
  `;
  
  // 计算位置：默认基于选中文本的位置
  const viewportWidth = window.innerWidth || document.documentElement.clientWidth;
  const viewportHeight = window.innerHeight || document.documentElement.clientHeight;
  
  // 使用新的位置计算函数
  const position = calculatePopupPosition(selectionRect, true);
  
  // 应用位置样式
  setStyles(popup, {
    ...position,
    zIndex: '10000',
    background: '#ffffff',
    padding: '8px 12px',
    borderRadius: '6px',
    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.2)',
    width: 'auto', // 自适应宽度
    maxWidth: '320px', // 保持最大宽度限制
    height: 'auto', // 自适应高度
    maxHeight: '220px', // 保持最大高度限制
    overflow: 'hidden',
    fontSize: '14px',
    lineHeight: '2.0',
    color: '#333',
    display: 'flex',
    flexDirection: 'column',
    wordBreak: 'break-word', // 添加文字换行
    whiteSpace: 'pre-wrap', // 保留换行符并自动换行
  });
  
  // 为内容添加样式
  const content = popup.querySelector('.tristan-translate-normal-popup-content');
  setStyles(content, {
    marginRight: '15px',
    flex: '1',
    overflowY: 'auto',
    wordBreak: 'break-word',
    whiteSpace: 'pre-wrap',
    paddingRight: '5px',
    height: 'auto', // 自适应高度
    minHeight: '20px', // 设置最小高度，防止内容过少时的显示问题
  });
  
  // 为关闭按钮添加样式和事件
  const closeButton = popup.querySelector('.tristan-translate-normal-popup-close');
  setStyles(closeButton, {
    position: 'absolute',
    top: '5px',
    right: '5px',
    width: '20px',
    height: '20px',
    lineHeight: '18px',
    textAlign: 'center',
    cursor: 'pointer',
    fontSize: '16px',
    fontWeight: 'bold',
    color: '#999',
    borderRadius: '50%',
    flexShrink: '0',
    userSelect: 'none', // 关闭按钮不可选中
    pointerEvents: 'auto' // 允许鼠标交互
  });
  
  // 添加鼠标悬停效果
  closeButton.addEventListener('mouseover', function() {
    setStyles(this, { color: '#333', background: '#f0f0f0' });
  });
  
  closeButton.addEventListener('mouseout', function() {
    setStyles(this, { color: '#999', background: 'transparent' });
  });
  
  // 添加点击关闭事件
  closeButton.addEventListener('click', function() {
    popup.remove();
  });
  
  // 添加到页面
  if (document.body) {
    document.body.appendChild(popup);
  } else {
    // 如果body还不存在，等待document完成加载
    const appendPopupWhenReady = () => {
      if (document.body) {
        document.body.appendChild(popup);
      } else {
        setTimeout(appendPopupWhenReady, 10);
      }
    };
    setTimeout(appendPopupWhenReady, 10);
  }
  
  return popup;
}

// 创建加载提示
function createLoadingNotification(text) {
  const notification = document.createElement('div');
  notification.id = 'tristan-loading-notification';
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: #4a90e2;
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    z-index: 10000;
    font-family: Arial, sans-serif;
    font-size: 14px;
    max-width: 300px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  `;
  
  const textSpan = document.createElement('span');
  textSpan.textContent = '正在翻译...';
  notification.appendChild(textSpan);
  
  if (document.body) {
    document.body.appendChild(notification);
  } else {
    // 如果body还不存在，等待document完成加载
    const appendNotificationWhenReady = () => {
      if (document.body) {
        document.body.appendChild(notification);
      } else {
        setTimeout(appendNotificationWhenReady, 10);
      }
    };
    setTimeout(appendNotificationWhenReady, 10);
  }
  
  // 3秒后自动关闭
  setTimeout(() => {
    if (notification.parentNode) {
      notification.parentNode.removeChild(notification);
    }
  }, 3000);
}

// 创建翻译结果提示
function createTranslationNotification(originalText, translatedText) {
  const notification = document.createElement('div');
  notification.id = 'tristan-translation-notification';
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: white;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    z-index: 10000;
    font-family: Arial, sans-serif;
    font-size: 14px;
    max-width: 400px;
  `;
  
  const originalDiv = document.createElement('div');
  originalDiv.style.cssText = `
    margin-bottom: 10px;
    color: #666;
  `;
  originalDiv.textContent = originalText;
  
  const translatedDiv = document.createElement('div');
  translatedDiv.style.cssText = `
    color: #333;
    font-weight: bold;
  `;
  translatedDiv.textContent = translatedText;
  
  const closeButton = document.createElement('button');
  closeButton.textContent = '×';
  closeButton.style.cssText = `
    position: absolute;
    top: 10px;
    right: 10px;
    background: none;
    border: none;
    color: #999;
    font-size: 20px;
    cursor: pointer;
    padding: 0 5px;
  `;
  closeButton.onclick = () => {
    if (notification.parentNode) {
      notification.parentNode.removeChild(notification);
    }
  };
  
  notification.appendChild(closeButton);
  notification.appendChild(originalDiv);
  notification.appendChild(translatedDiv);
  
  if (document.body) {
    document.body.appendChild(notification);
  } else {
    // 如果body还不存在，等待document完成加载
    const appendNotificationWhenReady = () => {
      if (document.body) {
        document.body.appendChild(notification);
      } else {
        setTimeout(appendNotificationWhenReady, 10);
      }
    };
    setTimeout(appendNotificationWhenReady, 10);
  }
  
  // 10秒后自动关闭
  setTimeout(() => {
    if (notification.parentNode) {
      notification.parentNode.removeChild(notification);
    }
  }, 10000);
}

// 监听来自后台脚本的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === "showLoading") {
    createLoadingIndicator();
  } else if (request.action === "showTranslation") {
    // 移除加载提示框
    const loadingIndicators = document.querySelectorAll('.tristan-translate-loading');
    loadingIndicators.forEach(indicator => {
      try {
        indicator.remove();
      } catch (e) {
        // 忽略可能的错误
      }
    });
    
    // 清除加载检查定时器
    if (window.tristanLoadingTimer) {
      clearInterval(window.tristanLoadingTimer);
      window.tristanLoadingTimer = null;
    }
    
    // 获取当前活动的输入元素
    const activeElement = getSelectedInputElement();
    
    // 尝试替换文本
    let success = false;
    
    // 如果有活动输入元素，尝试替换其内容
    if (activeElement) {
      success = setInputValue(activeElement, request.text);
    }
    
    // 如果替换失败，显示翻译结果提示
    if (!success) {
      const selectionRect = getSelectionRect();
      if (selectionRect) {
        createNormalTextTranslationPopup(request.text, selectionRect);
      } else {
        createTranslationNotification(window.getSelection().toString(), request.text);
      }
    }
  } else if (request.action === "showErrorNotification") {
    // 移除加载提示框
    const loadingIndicators = document.querySelectorAll('.tristan-translate-loading');
    loadingIndicators.forEach(indicator => {
      try {
        indicator.remove();
      } catch (e) {
        // 忽略可能的错误
      }
    });
    
    // 清除加载检查定时器
    if (window.tristanLoadingTimer) {
      clearInterval(window.tristanLoadingTimer);
      window.tristanLoadingTimer = null;
    }
    
    createErrorNotification(request.message);
  }
});

// 帮助函数：复制到剪贴板但不显示通知
async function copyToClipboardSilently(text) {
  logDebug('尝试复制到剪贴板但不显示通知', { textLength: text.length });
  
  // 尝试方法1: 使用现代Clipboard API
  if (navigator.clipboard && window.isSecureContext) {
    try {
      await navigator.clipboard.writeText(text);
      logDebug('使用现代Clipboard API复制成功');
      return true;
    } catch (e) {
      logDebug('现代Clipboard API失败', e);
      // 继续尝试其他方法
    }
  }
  
  // 尝试方法2: 使用传统execCommand方法
  try {
    // 创建临时输入框
    const tempInput = document.createElement('textarea');
    tempInput.value = text;
    
    // 确保元素完全不可见但可以选中
    setStyles(tempInput, {
      position: 'fixed',
      left: '-9999px',
      top: '-9999px',
      opacity: '0',
      zIndex: '-9999',
      pointerEvents: 'none',
      userSelect: 'text'
    });
    
    // 添加到DOM并选中
    if (document.body) {
      document.body.appendChild(tempInput);
      tempInput.select();
      tempInput.setSelectionRange(0, 99999); // 适用于移动设备
      
      // 尝试复制到剪贴板
      const success = document.execCommand('copy');
      
      // 移除临时输入框
      document.body.removeChild(tempInput);
      
      if (success) {
        logDebug('使用execCommand复制成功');
        return true;
      }
    } else {
      // 如果body还不存在，等待document完成加载
      const copyWhenReady = () => {
        if (document.body) {
          document.body.appendChild(tempInput);
          tempInput.select();
          tempInput.setSelectionRange(0, 99999); // 适用于移动设备
          
          // 尝试复制到剪贴板
          const success = document.execCommand('copy');
          
          // 移除临时输入框
          document.body.removeChild(tempInput);
          
          if (success) {
            logDebug('使用execCommand复制成功');
            return true;
          }
        } else {
          setTimeout(copyWhenReady, 10);
          return false;
        }
      };
      const result = await new Promise(resolve => {
        setTimeout(() => {
          resolve(copyWhenReady());
        }, 10);
      });
      if (result) return true;
    }
  } catch (e) {
    logDebug('复制到剪贴板失败', e);
  }
  
  // 如果所有方法都失败，不显示任何提示，只记录日志
  logDebug('所有复制方法都失败，但不显示提示');
  return false;
}

// 添加全局样式
if (!document.getElementById('tristan-translate-styles')) {
  const style = document.createElement('style');
  style.id = 'tristan-translate-styles';
  style.textContent = `
    .tristan-translate-notification {
      position: fixed;
      z-index: 10000;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
      font-size: 14px;
      line-height: 1.5;
      color: #333;
      background: #ffffff;
      border-radius: 6px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
      padding: 12px 15px;
      max-width: 320px;
      word-break: break-word;
      white-space: pre-wrap;
      transition: opacity 0.3s ease-in-out;
    }

    .tristan-translate-error {
      background: #fff2f0;
      border: 1px solid #ffccc7;
      color: #ff4d4f;
    }

    .tristan-translate-loading {
      display: flex;
      align-items: center;
      background: #ffffff;
      padding: 8px 12px;
      border-radius: 6px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      z-index: 10000;
      pointer-events: none;
      user-select: none;
    }

    .tristan-translate-loading-spinner {
      width: 20px;
      height: 20px;
      margin-right: 8px;
      border: 2px solid #f3f3f3;
      border-top: 2px solid #4a90e2;
      border-radius: 50%;
      animation: tristan-translate-spin 1s linear infinite;
      flex-shrink: 0;
    }

    .tristan-translate-loading-text {
      color: #666;
      font-size: 14px;
      white-space: nowrap;
      flex-shrink: 1;
    }

    .tristan-translate-normal-popup {
      position: fixed;
      z-index: 10000;
      background: #ffffff;
      padding: 8px 12px;
      border-radius: 6px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
      max-width: 320px;
      max-height: 220px;
      overflow: hidden;
      font-size: 14px;
      line-height: 2.0;
      color: #333;
      display: flex;
      flex-direction: column;
      word-break: break-word;
      white-space: pre-wrap;
    }

    .tristan-translate-normal-popup-content {
      margin-right: 15px;
      flex: 1;
      overflow-y: auto;
      word-break: break-word;
      white-space: pre-wrap;
      padding-right: 5px;
      height: auto;
      min-height: 20px;
    }

    .tristan-translate-normal-popup-close {
      position: absolute;
      top: 5px;
      right: 5px;
      width: 20px;
      height: 20px;
      line-height: 18px;
      text-align: center;
      cursor: pointer;
      font-size: 16px;
      font-weight: bold;
      color: #999;
      border-radius: 50%;
      flex-shrink: 0;
      user-select: none;
      pointer-events: auto;
    }

    .tristan-translate-normal-popup-close:hover {
      color: #333;
      background: #f0f0f0;
    }

    @keyframes tristan-translate-spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  `;
  
  // 使用MutationObserver确保styles被正确添加
  const appendStyleToHead = () => {
    if (document.head) {
      document.head.appendChild(style);
    } else {
      // 如果head还不存在，设置一个定时器等待document完成加载
      window.setTimeout(appendStyleToHead, 10);
    }
  };
  
  appendStyleToHead();
}
