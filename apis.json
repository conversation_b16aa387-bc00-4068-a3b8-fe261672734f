{"apis": [{"name": "SiliconCloud", "endpoint": "https://api.siliconflow.cn/v1/chat/completions", "key": "sk-izjhqiokvpczpxriuuhbvudtmvmuohnsukwtatlfhsjxtanb", "defaultModel": "deepseek-ai/DeepSeek-V3", "models": ["deepseek-ai/DeepSeek-R1", "deepseek-ai/DeepSeek-V3"]}, {"name": "AliYunAPI", "endpoint": "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions", "key": "sk-ba809f1224c344a38e3540712b79a8ad", "defaultModel": "deepseek-v3", "models": ["deepseek-r1", "deepseek-v3", "qwen-plus", "qwen-turbo", "qwen-plus-latest", "qwen-turbo-latest", "qwen-plus-2025-04-28", "qwen-turbo-2025-04-28"]}, {"name": "Volcengine", "endpoint": "https://ark.cn-beijing.volces.com/api/v3/chat/completions", "key": "99afbc85-3457-4b44-a04c-e0183c2d4f4d", "defaultModel": "deepseek-v3-250324", "models": ["deepseek-v3-250324", "doubao-seed-1-6-250615", "doubao-seed-1-6-flash-250615", "doubao-1-5-pro-256k-250115", "doubao-1-5-pro-32k-250115", "doubao-1-5-lite-32k-250115"]}, {"name": "ModelScope", "endpoint": "https://api-inference.modelscope.cn/v1/chat/completions", "key": "1743bcca-fd35-4500-ac9d-7afe4a81ff7e", "defaultModel": "deepseek-ai/DeepSeek-V3", "models": ["deepseek-ai/DeepSeek-R1", "deepseek-ai/DeepSeek-V3", "deepseek-ai/DeepSeek-R1-0528", "deepseek-ai/DeepSeek-V3-0324"]}, {"name": "OpenRouter", "endpoint": "https://openrouter.ai/api/v1/chat/completions", "key": "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", "defaultModel": "deepseek/deepseek-chat:free", "models": ["deepseek/deepseek-r1:free", "deepseek/deepseek-chat:free", "deepseek/deepseek-chat-v3-0324:free"]}, {"name": "<PERSON><PERSON>", "endpoint": "https://llm.chutes.ai/v1/chat/completions", "key": "cpk_d21f9d4f3a534cd1bdbd5e8caa893edb.85492955e4dd5645a99410eeaca1e12d.snYc2ozYOpLpRXWPyRgp88MLrN8OkWWI", "defaultModel": "deepseek-ai/DeepSeek-V3", "models": ["deepseek-ai/DeepSeek-R1", "deepseek-ai/DeepSeek-V3", "deepseek-ai/DeepSeek-R1-0528", "deepseek-ai/DeepSeek-V3-0324"]}], "defaultApi": "AliYunAPI", "languages": ["中文", "英语", "法语", "德语", "俄语", "日语", "韩语", "荷兰语", "瑞典语", "印尼语", "西班牙语", "意大利语", "葡萄牙语", "比利时语"], "defaultLanguage": "法语"}